import { OrderStatus } from '@/constant';
import { index } from '@/services/order';
import { Card, Calendar, Badge, List, Avatar, Space, Button, message, Spin, Tag } from 'antd';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import {
  UserOutlined,
  PhoneOutlined,
  DollarOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';

interface TimelineViewProps {
  onViewDetail: (order: API.Order) => void;
  onAssign: (order: API.Order) => void;
}

const TimelineView: React.FC<TimelineViewProps> = ({
  onViewDetail,
  onAssign,
}) => {
  const [loading, setLoading] = useState(true);
  const [orders, setOrders] = useState<API.Order[]>([]);
  const [selectedDate, setSelectedDate] = useState<Dayjs>(dayjs());
  const [selectedOrders, setSelectedOrders] = useState<API.Order[]>([]);

  const fetchOrders = async () => {
    setLoading(true);
    try {
      const { errCode, msg, data } = await index({});
      if (errCode) {
        message.error(msg || '获取订单列表失败');
      } else {
        setOrders(data?.list || []);
      }
    } catch (error) {
      console.error('获取订单失败:', error);
      message.error('获取订单失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOrders();
  }, []);

  // 根据选中日期筛选订单
  useEffect(() => {
    const dateStr = selectedDate.format('YYYY-MM-DD');
    const filtered = orders.filter(order => {
      if (!order.serviceTime) return false;
      const orderDate = dayjs(order.serviceTime).format('YYYY-MM-DD');
      return orderDate === dateStr;
    });
    
    // 按预约时间排序
    filtered.sort((a, b) => {
      if (!a.serviceTime || !b.serviceTime) return 0;
      return new Date(a.serviceTime).getTime() - new Date(b.serviceTime).getTime();
    });
    
    setSelectedOrders(filtered);
  }, [selectedDate, orders]);

  // 获取日期的订单数量
  const getOrderCountForDate = (date: Dayjs) => {
    const dateStr = date.format('YYYY-MM-DD');
    return orders.filter(order => {
      if (!order.serviceTime) return false;
      const orderDate = dayjs(order.serviceTime).format('YYYY-MM-DD');
      return orderDate === dateStr;
    }).length;
  };

  // 日历单元格渲染
  const dateCellRender = (value: Dayjs) => {
    const count = getOrderCountForDate(value);
    if (count === 0) return null;
    
    return (
      <div style={{ textAlign: 'center' }}>
        <Badge count={count} style={{ backgroundColor: '#52c41a' }} />
      </div>
    );
  };

  // 状态颜色配置
  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      [OrderStatus.待付款]: 'orange',
      [OrderStatus.待接单]: 'blue',
      [OrderStatus.待服务]: 'cyan',
      [OrderStatus.已出发]: 'geekblue',
      [OrderStatus.服务中]: 'purple',
      [OrderStatus.已完成]: 'green',
      [OrderStatus.已评价]: 'lime',
      [OrderStatus.已取消]: 'red',
      [OrderStatus.退款中]: 'volcano',
      [OrderStatus.已退款]: 'magenta',
    };
    return colorMap[status] || 'default';
  };

  const renderOrderItem = (order: API.Order) => {
    const serviceName = order.orderDetails?.[0]?.service?.serviceName || '未知服务';
    const serviceType = order.orderDetails?.[0]?.service?.serviceType?.name || '未知类型';
    const serviceTime = order.serviceTime ? dayjs(order.serviceTime).format('HH:mm') : '未设置';
    
    return (
      <List.Item
        key={order.id}
        style={{ cursor: 'pointer' }}
        onClick={() => onViewDetail(order)}
        actions={[
          <Button
            key="detail"
            type="link"
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              onViewDetail(order);
            }}
          >
            详情
          </Button>,
          ...(order.status === OrderStatus.待接单 ? [
            <Button
              key="assign"
              type="primary"
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                onAssign(order);
              }}
            >
              派单
            </Button>
          ] : []),
          ...([OrderStatus.待服务, OrderStatus.已出发].includes(order.status as any) ? [
            <Button
              key="reassign"
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                onAssign(order);
              }}
            >
              改派
            </Button>
          ] : []),
        ]}
      >
        <List.Item.Meta
          avatar={
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#1890ff' }}>
                {serviceTime}
              </div>
              <Tag color={getStatusColor(order.status)} size="small">
                {order.status}
              </Tag>
            </div>
          }
          title={
            <div>
              <Space>
                <span style={{ fontWeight: 'bold' }}>{order.sn}</span>
                <span>{serviceName}</span>
                <span style={{ color: '#666' }}>({serviceType})</span>
              </Space>
            </div>
          }
          description={
            <div>
              <div style={{ marginBottom: '4px' }}>
                <Space size="small">
                  <UserOutlined style={{ color: '#1890ff' }} />
                  <span>{order.customer?.nickname || '未知客户'}</span>
                  <PhoneOutlined style={{ color: '#52c41a' }} />
                  <span>{order.customer?.phone || '未知电话'}</span>
                </Space>
              </div>
              <div style={{ marginBottom: '4px' }}>
                <Space size="small">
                  <DollarOutlined style={{ color: '#fa8c16' }} />
                  <span>¥{order.totalFee}</span>
                  {order.employee && (
                    <>
                      <Avatar size="small" icon={<UserOutlined />} />
                      <span>{order.employee.name}</span>
                    </>
                  )}
                </Space>
              </div>
              {order.address && (
                <div style={{ color: '#666', fontSize: '12px' }}>
                  地址: {order.address}
                </div>
              )}
            </div>
          }
        />
      </List.Item>
    );
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div style={{ padding: '16px' }}>
      <div style={{ display: 'flex', gap: '16px', height: '600px' }}>
        {/* 左侧日历 */}
        <Card 
          title="预约日历" 
          style={{ flex: 1, minWidth: '400px' }}
          bodyStyle={{ padding: '12px' }}
        >
          <Calendar
            fullscreen={false}
            value={selectedDate}
            onSelect={setSelectedDate}
            dateCellRender={dateCellRender}
          />
        </Card>

        {/* 右侧订单列表 */}
        <Card 
          title={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span>
                {selectedDate.format('YYYY年MM月DD日')} 预约安排
              </span>
              <Badge count={selectedOrders.length} />
            </div>
          }
          style={{ flex: 1, minWidth: '500px' }}
          bodyStyle={{ padding: '0', height: '520px', overflowY: 'auto' }}
        >
          {selectedOrders.length > 0 ? (
            <List
              dataSource={selectedOrders}
              renderItem={renderOrderItem}
              split={true}
            />
          ) : (
            <div style={{ textAlign: 'center', padding: '50px', color: '#999' }}>
              <ClockCircleOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
              <div>该日期暂无预约</div>
            </div>
          )}
        </Card>
      </div>
    </div>
  );
};

export default TimelineView;
