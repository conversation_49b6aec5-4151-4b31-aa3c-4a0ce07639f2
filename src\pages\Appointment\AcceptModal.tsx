import { OrderStatus } from '@/constant';
import { DictionarieState } from '@/models/dictionarie';
import { index } from '@/services/employees';
import { connect } from '@umijs/max';
import {
  Button,
  Drawer,
  Flex,
  Table,
  TableColumnsType,
  Tabs,
  Card,
  Row,
  Col,
  Avatar,
  Tag,
  Space,
  Timeline,
  Badge,
  Tooltip,
  Rate,
  Progress,
  Divider
} from 'antd';
import { TableRowSelection } from 'antd/es/table/interface';
import React, { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import {
  UserOutlined,
  PhoneOutlined,
  ClockCircleOutlined,
  EnvironmentOutlined,
  CarOutlined,
  StarOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';

type EditModalProps = {
  open: boolean;
  current?: API.Order;
  onSave: (employeeId: number) => Promise<void>;
  onClose: () => void;
  dictionarie: DictionarieState;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  current,
  onSave,
  onClose,
}) => {
  const [selectedRowKey, setSelectedRowKey] = useState<React.Key>();
  const [baseList, setBaseList] = useState<API.Employee[]>([]);
  const [activeTab, setActiveTab] = useState<string>('list');

  useEffect(() => {
    if (open) {
      setSelectedRowKey(current?.employeeId);
    } else {
      setSelectedRowKey(undefined);
    }
  }, [open, current?.employeeId]);

  // 计算员工工作负载
  const calculateWorkload = (employee: API.Employee) => {
    const orders = employee.orders || [];
    const activeOrders = orders.filter(order =>
      [OrderStatus.待服务, OrderStatus.已出发, OrderStatus.服务中].includes(order.status as any)
    );
    return {
      total: activeOrders.length,
      pending: orders.filter(order => order.status === OrderStatus.待服务).length,
      inProgress: orders.filter(order => order.status === OrderStatus.服务中).length,
      onWay: orders.filter(order => order.status === OrderStatus.已出发).length,
    };
  };

  // 检查时间冲突
  const checkTimeConflict = (employee: API.Employee, newOrderTime?: Date) => {
    if (!newOrderTime || !employee.orders) return false;

    const newTime = dayjs(newOrderTime);
    const conflicts = employee.orders.filter(order => {
      if (!order.serviceTime) return false;
      const orderTime = dayjs(order.serviceTime);
      // 检查前后2小时内是否有冲突
      return Math.abs(newTime.diff(orderTime, 'hour')) < 2 &&
             [OrderStatus.待服务, OrderStatus.已出发, OrderStatus.服务中].includes(order.status as any);
    });

    return conflicts.length > 0;
  };

  // 计算距离（简化版，实际应该使用地图API）
  const calculateDistance = (employee: API.Employee, orderAddress: { longitude: number; latitude: number }) => {
    if (!employee.vehicle?.longitude || !employee.vehicle?.latitude) return null;

    const dx = employee.vehicle.longitude - orderAddress.longitude;
    const dy = employee.vehicle.latitude - orderAddress.latitude;
    const distance = Math.sqrt(dx * dx + dy * dy) * 111; // 粗略转换为公里

    return Math.round(distance * 10) / 10;
  };

  // 获取员工状态颜色
  const getEmployeeStatusColor = (employee: API.Employee) => {
    const workload = calculateWorkload(employee);
    if (workload.total === 0) return 'green';
    if (workload.total <= 2) return 'blue';
    if (workload.total <= 4) return 'orange';
    return 'red';
  };

  // 获取推荐度评分
  const getRecommendationScore = (employee: API.Employee) => {
    const workload = calculateWorkload(employee);
    const hasConflict = checkTimeConflict(employee, current?.serviceTime);
    const distance = current ? calculateDistance(employee, {
      longitude: current.longitude,
      latitude: current.latitude
    }) : null;

    let score = 5;

    // 工作负载影响
    if (workload.total > 4) score -= 2;
    else if (workload.total > 2) score -= 1;

    // 时间冲突影响
    if (hasConflict) score -= 2;

    // 距离影响
    if (distance && distance > 10) score -= 1;
    if (distance && distance > 20) score -= 1;

    // 评分影响
    if (employee.rating && employee.rating < 3) score -= 1;

    // 状态影响
    if (employee.status === 0) score = 0;

    return Math.max(0, score);
  };

  // 渲染员工卡片
  const renderEmployeeCard = (employee: API.Employee) => {
    const workload = calculateWorkload(employee);
    const hasConflict = checkTimeConflict(employee, current?.serviceTime);
    const distance = current ? calculateDistance(employee, {
      longitude: current.longitude,
      latitude: current.latitude
    }) : null;
    const recommendationScore = getRecommendationScore(employee);
    const statusColor = getEmployeeStatusColor(employee);

    const isSelected = selectedRowKey === employee.id;
    const isDisabled = employee.status === 0;

    return (
      <Card
        key={employee.id}
        size="small"
        style={{
          marginBottom: '12px',
          cursor: isDisabled ? 'not-allowed' : 'pointer',
          border: isSelected ? '2px solid #1890ff' : '1px solid #d9d9d9',
          opacity: isDisabled ? 0.6 : 1,
        }}
        hoverable={!isDisabled}
        onClick={() => !isDisabled && setSelectedRowKey(employee.id)}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <div style={{ flex: 1 }}>
            {/* 员工基本信息 */}
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
              <Avatar
                src={employee.avatar}
                icon={<UserOutlined />}
                size="small"
                style={{ marginRight: '8px' }}
              />
              <div>
                <div style={{ fontWeight: 'bold', fontSize: '14px' }}>
                  {employee.name}
                  {employee.level && (
                    <Tag color="blue" size="small" style={{ marginLeft: '4px' }}>
                      {employee.level}级
                    </Tag>
                  )}
                </div>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  <PhoneOutlined style={{ marginRight: '4px' }} />
                  {employee.phone}
                </div>
              </div>
            </div>

            {/* 评分和经验 */}
            <div style={{ marginBottom: '8px' }}>
              <Space size="small">
                {employee.rating && (
                  <div style={{ fontSize: '12px' }}>
                    <StarOutlined style={{ color: '#faad14', marginRight: '2px' }} />
                    {employee.rating.toFixed(1)}
                  </div>
                )}
                {employee.workExp && (
                  <div style={{ fontSize: '12px', color: '#666' }}>
                    {employee.workExp}个月经验
                  </div>
                )}
              </Space>
            </div>

            {/* 工作负载 */}
            <div style={{ marginBottom: '8px' }}>
              <div style={{ fontSize: '12px', marginBottom: '4px' }}>
                工作负载:
                <Tag color={statusColor} size="small" style={{ marginLeft: '4px' }}>
                  {workload.total}单
                </Tag>
              </div>
              <div style={{ fontSize: '11px', color: '#666' }}>
                待服务: {workload.pending} | 进行中: {workload.inProgress} | 已出发: {workload.onWay}
              </div>
            </div>

            {/* 位置和距离 */}
            {employee.vehicle && (
              <div style={{ marginBottom: '8px' }}>
                <Space size="small">
                  <CarOutlined style={{ color: '#1890ff' }} />
                  <span style={{ fontSize: '12px' }}>{employee.vehicle.plateNumber}</span>
                  {distance !== null && (
                    <span style={{ fontSize: '12px', color: '#666' }}>
                      距离: {distance}km
                    </span>
                  )}
                </Space>
              </div>
            )}

            {/* 警告信息 */}
            {hasConflict && (
              <div style={{ marginBottom: '8px' }}>
                <Tag color="red" size="small">
                  <ExclamationCircleOutlined style={{ marginRight: '2px' }} />
                  时间冲突
                </Tag>
              </div>
            )}
          </div>

          {/* 推荐度评分 */}
          <div style={{ textAlign: 'center', minWidth: '60px' }}>
            <div style={{ fontSize: '12px', color: '#666', marginBottom: '4px' }}>推荐度</div>
            <Rate
              disabled
              allowHalf
              value={recommendationScore}
              style={{ fontSize: '12px' }}
            />
            <div style={{ fontSize: '10px', color: '#999', marginTop: '2px' }}>
              {recommendationScore}/5
            </div>
          </div>
        </div>
      </Card>
    );
  };

  const columns: TableColumnsType<API.Employee> = [
    { title: 'id', dataIndex: 'id', hidden: true },
    { title: '姓名', dataIndex: 'name', width: 100 },
    { title: '手机号', dataIndex: 'phone', width: 120 },
    {
      title: '等级',
      dataIndex: 'level',
      width: 60,
      align: 'center',
      render: (level) => level ? `${level}级` : '-',
    },
    {
      title: '评分',
      dataIndex: 'rating',
      width: 80,
      align: 'center',
      render: (rating) => rating ? (
        <Space>
          <StarOutlined style={{ color: '#faad14' }} />
          {rating.toFixed(1)}
        </Space>
      ) : '-',
    },
    {
      title: '工作负载',
      dataIndex: 'workload',
      width: 100,
      align: 'center',
      render: (_, record) => {
        const workload = calculateWorkload(record);
        const color = getEmployeeStatusColor(record);
        return (
          <Tooltip title={`待服务: ${workload.pending}, 进行中: ${workload.inProgress}, 已出发: ${workload.onWay}`}>
            <Tag color={color}>{workload.total}单</Tag>
          </Tooltip>
        );
      },
    },
    {
      title: '距离',
      dataIndex: 'distance',
      width: 80,
      align: 'center',
      render: (_, record) => {
        if (!current) return '-';
        const distance = calculateDistance(record, {
          longitude: current.longitude,
          latitude: current.latitude
        });
        return distance !== null ? `${distance}km` : '-';
      },
    },
    {
      title: '推荐度',
      dataIndex: 'recommendation',
      width: 100,
      align: 'center',
      render: (_, record) => {
        const score = getRecommendationScore(record);
        return <Rate disabled allowHalf value={score} style={{ fontSize: '12px' }} />;
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 80,
      align: 'center',
      render: (status, record) => {
        const hasConflict = checkTimeConflict(record, current?.serviceTime);
        if (status === 0) return <Tag color="red">禁用</Tag>;
        if (hasConflict) return <Tag color="orange">时间冲突</Tag>;
        return <Tag color="green">可用</Tag>;
      },
    },
  ];

  // 渲染员工时间轴
  const renderEmployeeTimeline = (employee: API.Employee) => {
    const orders = employee.orders || [];
    const todayOrders = orders.filter(order => {
      if (!order.serviceTime) return false;
      return dayjs(order.serviceTime).isSame(dayjs(), 'day');
    }).sort((a, b) => {
      if (!a.serviceTime || !b.serviceTime) return 0;
      return new Date(a.serviceTime).getTime() - new Date(b.serviceTime).getTime();
    });

    const timelineItems = todayOrders.map(order => ({
      color: order.status === OrderStatus.已完成 ? 'green' :
             order.status === OrderStatus.服务中 ? 'blue' :
             order.status === OrderStatus.已出发 ? 'orange' : 'gray',
      children: (
        <div>
          <div style={{ fontWeight: 'bold', fontSize: '12px' }}>
            {order.serviceTime ? dayjs(order.serviceTime).format('HH:mm') : '未设置时间'}
          </div>
          <div style={{ fontSize: '11px', color: '#666' }}>
            {order.orderDetails?.[0]?.service?.serviceName || '未知服务'}
          </div>
          <div style={{ fontSize: '11px', color: '#999' }}>
            {order.address}
          </div>
          <Tag size="small" color={
            order.status === OrderStatus.已完成 ? 'green' :
            order.status === OrderStatus.服务中 ? 'blue' :
            order.status === OrderStatus.已出发 ? 'orange' : 'default'
          }>
            {order.status}
          </Tag>
        </div>
      ),
    }));

    // 添加新订单的预览
    if (current?.serviceTime) {
      const newOrderTime = dayjs(current.serviceTime);
      timelineItems.push({
        color: 'red',
        children: (
          <div style={{ border: '1px dashed #ff4d4f', padding: '4px', borderRadius: '4px' }}>
            <div style={{ fontWeight: 'bold', fontSize: '12px', color: '#ff4d4f' }}>
              {newOrderTime.format('HH:mm')} (新订单)
            </div>
            <div style={{ fontSize: '11px', color: '#666' }}>
              {current.orderDetails?.[0]?.service?.serviceName || '未知服务'}
            </div>
            <div style={{ fontSize: '11px', color: '#999' }}>
              {current.address}
            </div>
          </div>
        ),
      });

      // 重新排序
      timelineItems.sort((a, b) => {
        const timeA = a.children.props?.children?.[0]?.props?.children || '';
        const timeB = b.children.props?.children?.[0]?.props?.children || '';
        return timeA.localeCompare(timeB);
      });
    }

    return (
      <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
        <Timeline
          size="small"
          items={timelineItems.length > 0 ? timelineItems : [{
            children: <div style={{ color: '#999', fontSize: '12px' }}>今日暂无安排</div>
          }]}
        />
      </div>
    );
  };

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    console.log('selectedRowKeys changed: ', newSelectedRowKeys);
    if (newSelectedRowKeys.length === 0) {
      setSelectedRowKey(undefined);
      return;
    }
    setSelectedRowKey(newSelectedRowKeys[0]);
  };

  const rowSelection: TableRowSelection<API.Employee> = {
    type: 'radio',
    selectedRowKeys: selectedRowKey ? [selectedRowKey] : [],
    onChange: onSelectChange,
  };

  useEffect(() => {
    (async () => {
      const res = await index({});
      setBaseList(res.data?.list || []);
    })();
  }, []);

  // 获取可用员工列表（按推荐度排序）
  const availableEmployees = baseList
    .filter(emp => emp.status === 1)
    .sort((a, b) => getRecommendationScore(b) - getRecommendationScore(a));

  // 获取选中员工的详细信息
  const selectedEmployee = baseList.find(emp => emp.id === selectedRowKey);

  const tabItems = [
    {
      key: 'cards',
      label: `推荐员工 (${availableEmployees.length})`,
      children: (
        <div style={{ maxHeight: '500px', overflowY: 'auto' }}>
          {availableEmployees.map(renderEmployeeCard)}
          {availableEmployees.length === 0 && (
            <div style={{ textAlign: 'center', padding: '50px', color: '#999' }}>
              暂无可用员工
            </div>
          )}
        </div>
      ),
    },
    {
      key: 'list',
      label: '详细列表',
      children: (
        <Table<API.Employee>
          rowKey="id"
          rowSelection={rowSelection}
          columns={columns}
          dataSource={baseList}
          pagination={false}
          size="small"
          scroll={{ y: 400 }}
        />
      ),
    },
    {
      key: 'timeline',
      label: '时间安排',
      children: selectedEmployee ? (
        <div>
          <Card size="small" style={{ marginBottom: '16px' }}>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
              <Avatar
                src={selectedEmployee.avatar}
                icon={<UserOutlined />}
                size="small"
                style={{ marginRight: '8px' }}
              />
              <div>
                <div style={{ fontWeight: 'bold' }}>{selectedEmployee.name}</div>
                <div style={{ fontSize: '12px', color: '#666' }}>{selectedEmployee.phone}</div>
              </div>
            </div>
            <Divider style={{ margin: '8px 0' }} />
            <div style={{ fontSize: '12px', color: '#666' }}>今日工作安排</div>
          </Card>
          {renderEmployeeTimeline(selectedEmployee)}
        </div>
      ) : (
        <div style={{ textAlign: 'center', padding: '50px', color: '#999' }}>
          请先选择员工
        </div>
      ),
    },
  ];

  return (
    <Drawer
      title={
        <div>
          <div style={{ fontSize: '16px', fontWeight: 'bold' }}>
            派单 - {current?.orderDetails?.[0].service?.serviceName || '未选择'}
          </div>
          {current?.serviceTime && (
            <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
              <ClockCircleOutlined style={{ marginRight: '4px' }} />
              预约时间: {dayjs(current.serviceTime).format('YYYY-MM-DD HH:mm')}
            </div>
          )}
          {current?.address && (
            <div style={{ fontSize: '12px', color: '#666', marginTop: '2px' }}>
              <EnvironmentOutlined style={{ marginRight: '4px' }} />
              服务地址: {current.address}
            </div>
          )}
        </div>
      }
      width={600}
      open={open}
      onClose={onClose}
    >
      <Flex justify="space-between" align="center" style={{ marginBottom: '16px' }}>
        <div style={{ fontSize: '12px', color: '#666' }}>
          共 {baseList.length} 名员工，{availableEmployees.length} 名可用
        </div>
        <Button
          type="primary"
          disabled={!selectedRowKey}
          onClick={() => {
            if (selectedRowKey && current?.employeeId !== selectedRowKey) {
              onSave(selectedRowKey as number);
            }
          }}
        >
          确定派单
        </Button>
      </Flex>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
        size="small"
      />
    </Drawer>
  );
};

export default connect(({ dictionarie }) => ({ dictionarie }))(EditModal);
