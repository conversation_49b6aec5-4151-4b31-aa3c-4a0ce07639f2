# 派单界面优化说明

## 优化概述

原有的派单界面只显示员工姓名、手机号和待服务订单数，信息过于简单，无法帮助管理人员做出最佳的派单决策。经过优化后，现在提供了丰富的员工信息和智能推荐功能。

## 新增功能

### 1. 智能推荐系统
- **推荐度评分**: 基于多个维度计算员工的推荐度（0-5星）
- **评分算法**: 
  - 基础分: 5分
  - 工作负载: 超过4单扣2分，超过2单扣1分
  - 时间冲突: 有冲突扣2分
  - 距离因素: 超过10km扣1分，超过20km再扣1分
  - 服务评分: 低于3分扣1分
  - 员工状态: 禁用员工为0分

### 2. 三种视图模式

#### 推荐员工卡片视图
- **员工基本信息**: 头像、姓名、等级、手机号
- **服务评分**: 显示员工的历史服务评分
- **工作负载**: 实时显示待服务、进行中、已出发订单数
- **位置信息**: 显示车牌号和距离服务地址的距离
- **时间冲突警告**: 自动检测并标注时间冲突
- **推荐度评分**: 5星评分系统，直观显示推荐程度

#### 详细列表视图
- **完整信息表格**: 包含所有关键信息的表格视图
- **工作负载提示**: 鼠标悬停显示详细的工作分布
- **状态标识**: 清晰标注员工可用状态和时间冲突
- **距离计算**: 显示员工当前位置到服务地址的距离

#### 时间安排视图
- **今日时间轴**: 显示选中员工今日的完整工作安排
- **订单详情**: 每个时间点的服务内容和地址
- **新订单预览**: 以虚线框显示即将派发的新订单
- **时间冲突检测**: 自动检测并高亮显示时间冲突

### 3. 智能分析功能

#### 工作负载分析
```typescript
interface Workload {
  total: number;      // 总活跃订单数
  pending: number;    // 待服务订单数
  inProgress: number; // 服务中订单数
  onWay: number;      // 已出发订单数
}
```

#### 时间冲突检测
- 检测前后2小时内是否有其他服务安排
- 自动标注冲突订单
- 提供冲突警告提示

#### 距离计算
- 基于员工车辆位置和服务地址计算距离
- 考虑距离因素影响推荐度
- 帮助优化路线安排

### 4. 用户体验优化

#### 视觉设计
- **状态颜色编码**: 
  - 绿色: 空闲（0单）
  - 蓝色: 轻度负载（1-2单）
  - 橙色: 中度负载（3-4单）
  - 红色: 重度负载（5单以上）

#### 交互优化
- **一键选择**: 点击卡片即可选择员工
- **实时反馈**: 选中状态立即高亮显示
- **禁用处理**: 禁用员工自动置灰且不可选择

#### 信息展示
- **订单详情**: 在标题栏显示订单的服务名称、时间、地址
- **统计信息**: 显示总员工数和可用员工数
- **排序优化**: 按推荐度自动排序，最佳选择在前

## 技术实现

### 核心算法

#### 推荐度计算
```typescript
const getRecommendationScore = (employee: API.Employee) => {
  let score = 5; // 基础分
  
  // 工作负载影响
  if (workload.total > 4) score -= 2;
  else if (workload.total > 2) score -= 1;
  
  // 时间冲突影响
  if (hasConflict) score -= 2;
  
  // 距离影响
  if (distance > 10) score -= 1;
  if (distance > 20) score -= 1;
  
  // 评分影响
  if (rating < 3) score -= 1;
  
  // 状态影响
  if (status === 0) score = 0;
  
  return Math.max(0, score);
};
```

#### 时间冲突检测
```typescript
const checkTimeConflict = (employee: API.Employee, newOrderTime?: Date) => {
  const newTime = dayjs(newOrderTime);
  const conflicts = employee.orders.filter(order => {
    const orderTime = dayjs(order.serviceTime);
    return Math.abs(newTime.diff(orderTime, 'hour')) < 2;
  });
  return conflicts.length > 0;
};
```

#### 距离计算
```typescript
const calculateDistance = (employee: API.Employee, orderAddress) => {
  const dx = employee.vehicle.longitude - orderAddress.longitude;
  const dy = employee.vehicle.latitude - orderAddress.latitude;
  return Math.sqrt(dx * dx + dy * dy) * 111; // 转换为公里
};
```

### 数据结构

#### 员工扩展信息
- `orders[]`: 员工的所有订单列表
- `vehicle`: 关联的车辆信息（包含位置）
- `rating`: 服务评分
- `level`: 接单等级
- `workExp`: 工作经验

#### 订单信息
- `serviceTime`: 预约服务时间
- `longitude/latitude`: 服务地址坐标
- `address`: 服务地址描述
- `status`: 订单状态

## 使用指南

### 派单流程
1. **查看订单信息**: 在标题栏确认服务内容、时间、地址
2. **选择视图模式**: 
   - 新手推荐使用"推荐员工"视图
   - 需要详细对比使用"详细列表"视图
   - 需要查看时间安排使用"时间安排"视图
3. **选择员工**: 点击卡片或表格行选择员工
4. **确认派单**: 点击"确定派单"按钮完成派单

### 最佳实践
1. **优先选择高推荐度员工**: 5星推荐度的员工通常是最佳选择
2. **注意时间冲突**: 避免选择有时间冲突警告的员工
3. **考虑距离因素**: 选择距离较近的员工可以减少路程时间
4. **平衡工作负载**: 避免给已经负载很重的员工继续派单

### 状态说明
- **绿色标签**: 员工空闲，可以立即接单
- **蓝色标签**: 员工负载较轻，可以接单
- **橙色标签**: 员工负载适中，需要考虑时间安排
- **红色标签**: 员工负载较重，建议谨慎派单
- **时间冲突**: 红色警告标签，表示时间安排有冲突

## 优势总结

1. **智能推荐**: 自动计算最佳员工选择，提高派单效率
2. **信息丰富**: 提供全面的员工和订单信息，支持精准决策
3. **冲突预警**: 自动检测时间冲突，避免派单错误
4. **多视图支持**: 满足不同场景和用户习惯的需求
5. **用户友好**: 直观的界面设计，降低操作难度

通过这次优化，派单不再是简单的人员分配，而是基于数据分析的智能决策过程，大大提升了派单的准确性和效率。
